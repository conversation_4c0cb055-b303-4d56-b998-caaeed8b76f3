const asyncHandler = require('express-async-handler');
const Faq = require('../models/Faq');

// @desc    Get all FAQs
// @route   GET /api/faqs
// @access  Public
const getFaqs = asyncHandler(async (req, res) => {
  const { category } = req.query;
  
  // Build filter object
  const filter = { isActive: true };
  if (category) filter.category = category;
  
  const faqs = await Faq.find(filter).sort({ order: 1 });
  res.status(200).json(faqs);
});

// @desc    Create a FAQ
// @route   POST /api/faqs
// @access  Private/Admin
const createFaq = asyncHandler(async (req, res) => {
  const { question, answer, category, order } = req.body;

  if (!question || !answer) {
    res.status(400);
    throw new Error('Please provide question and answer');
  }

  const faq = await Faq.create({
    question,
    answer,
    category: category || 'general',
    order: order || 0,
    isActive: true
  });

  res.status(201).json(faq);
});

// @desc    Update a FAQ
// @route   PUT /api/faqs/:id
// @access  Private/Admin
const updateFaq = asyncHandler(async (req, res) => {
  const faq = await Faq.findById(req.params.id);
  
  if (!faq) {
    res.status(404);
    throw new Error('FAQ not found');
  }
  
  const updatedFaq = await Faq.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  );
  
  res.status(200).json(updatedFaq);
});

// @desc    Delete a FAQ
// @route   DELETE /api/faqs/:id
// @access  Private/Admin
const deleteFaq = asyncHandler(async (req, res) => {
  const faq = await Faq.findById(req.params.id);
  
  if (!faq) {
    res.status(404);
    throw new Error('FAQ not found');
  }
  
  // Soft delete - just mark as inactive
  faq.isActive = false;
  await faq.save();
  
  res.status(200).json({ message: 'FAQ removed' });
});

module.exports = {
  getFaqs,
  createFaq,
  updateFaq,
  deleteFaq
};