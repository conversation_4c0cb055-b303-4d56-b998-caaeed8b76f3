const mongoose = require('mongoose');

const faqSchema = mongoose.Schema(
  {
    question: {
      type: String,
      required: true
    },
    answer: {
      type: String,
      required: true
    },
    category: {
      type: String,
      enum: ['adoption', 'donation', 'volunteer', 'general'],
      default: 'general'
    },
    order: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Faq', faqSchema);