const mongoose = require('mongoose');

const donationSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false // Allow anonymous donations
    },
    amount: {
      type: Number,
      required: true
    },
    currency: {
      type: String,
      default: 'USD'
    },
    type: {
      type: String,
      enum: ['one-time', 'monthly', 'annual'],
      default: 'one-time'
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending'
    },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'paypal', 'bank_transfer', 'other'],
      default: 'credit_card'
    },
    notes: {
      type: String
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },
    dedicationType: {
      type: String,
      enum: ['in_memory', 'in_honor', 'none'],
      default: 'none'
    },
    dedicationName: {
      type: String
    },
    transactionId: {
      type: String
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Donation', donationSchema);