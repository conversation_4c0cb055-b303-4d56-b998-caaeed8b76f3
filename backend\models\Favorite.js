const mongoose = require('mongoose');

const favoriteSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User'
    },
    petId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Pet'
    }
  },
  {
    timestamps: true
  }
);

// Create a compound index to ensure a user can't favorite the same pet twice
favoriteSchema.index({ userId: 1, petId: 1 }, { unique: true });

module.exports = mongoose.model('Favorite', favoriteSchema);