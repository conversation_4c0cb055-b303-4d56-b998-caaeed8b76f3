const asyncHandler = require('express-async-handler');
const Donation = require('../models/Donation');

// @desc    Create a donation
// @route   POST /api/donations
// @access  Public
const createDonation = asyncHandler(async (req, res) => {
  const {
    amount,
    currency,
    type,
    paymentMethod,
    notes,
    isAnonymous,
    dedicationType,
    dedicationName
  } = req.body;

  if (!amount || amount <= 0) {
    res.status(400);
    throw new Error('Please provide a valid donation amount');
  }

  const donation = await Donation.create({
    userId: req.user ? req.user.id : null,
    amount,
    currency: currency || 'USD',
    type: type || 'one-time',
    paymentMethod: paymentMethod || 'credit_card',
    notes,
    isAnonymous: isAnonymous || false,
    dedicationType: dedicationType || 'none',
    dedicationName,
    status: 'pending',
    transactionId: `DON-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  });

  res.status(201).json(donation);
});

// @desc    Get donation statistics
// @route   GET /api/donations/stats
// @access  Public
const getDonationStats = asyncHandler(async (req, res) => {
  const totalDonations = await Donation.aggregate([
    { $match: { status: 'completed' } },
    { $group: { _id: null, total: { $sum: '$amount' } } }
  ]);

  const donorsCount = await Donation.distinct('userId', { 
    status: 'completed',
    userId: { $ne: null }
  });

  const anonymousDonations = await Donation.countDocuments({
    status: 'completed',
    userId: null
  });

  const donationGoal = 50000; // Set your goal
  const currentDonations = totalDonations.length > 0 ? totalDonations[0].total : 0;
  const progressPercentage = Math.min((currentDonations / donationGoal) * 100, 100);

  res.status(200).json({
    donationGoal,
    currentDonations,
    donorsCount: donorsCount.length + anonymousDonations,
    progressPercentage
  });
});

// @desc    Get donation testimonials
// @route   GET /api/donations/testimonials
// @access  Public
const getTestimonials = asyncHandler(async (req, res) => {
  // For now, return hardcoded testimonials
  // In a real app, you'd fetch these from the database
  const testimonials = [
    {
      name: 'Sarah Johnson',
      image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80',
      quote: 'Donating monthly has been so rewarding. I love getting updates about the animals my contributions are helping.',
      amount: 'Monthly Donor',
      years: '3 years'
    },
    {
      name: 'David Rodriguez',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80',
      quote: 'After adopting my dog from this rescue, I wanted to give back. My corporate matching program doubles my impact!',
      amount: 'Corporate Partner',
      years: '2 years'
    },
    {
      name: 'Emily Chen',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80',
      quote: 'I donate in memory of my beloved cat. It brings me joy knowing other animals are getting the care they need.',
      amount: 'One-Time Donor',
      years: 'Recurring supporter'
    }
  ];

  res.status(200).json(testimonials);
});

module.exports = {
  createDonation,
  getDonationStats,
  getTestimonials
};