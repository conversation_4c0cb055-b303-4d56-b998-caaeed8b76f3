const mongoose = require('mongoose');

const connectDB = async () => {
    // Return a promise that resolves when the connection is established
    return mongoose.connect(process.env.MONGO_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
    }).then(conn => {
        console.log(`MongoDB Connected: ${conn.connection.host}`);
        return conn;
    });
};

module.exports = connectDB;
