const mongoose = require('mongoose');

const eventSchema = mongoose.Schema(
  {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      required: true
    },
    time: {
      type: String,
      required: true
    },
    location: {
      type: String,
      required: true
    },
    imageUrl: {
      type: String,
      default: 'https://via.placeholder.com/300x200?text=Event'
    },
    eventType: {
      type: String,
      enum: ['adoption', 'fundraiser', 'volunteer', 'education', 'other'],
      default: 'other'
    },
    isVirtual: {
      type: Boolean,
      default: false
    },
    virtualLink: {
      type: String
    },
    registrationRequired: {
      type: Boolean,
      default: false
    },
    registrationLink: {
      type: String
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Event', eventSchema);