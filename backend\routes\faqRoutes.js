const express = require('express');
const router = express.Router();
const {
  getFaqs,
  createFaq,
  updateFaq,
  deleteFaq
} = require('../controllers/faqController');
const { protect, admin } = require('../middleware/authMiddleware');

// Public routes
router.get('/', getFaqs);

// Admin routes
router.post('/', protect, admin, createFaq);
router.put('/:id', protect, admin, updateFaq);
router.delete('/:id', protect, admin, deleteFaq);

module.exports = router;