const asyncHandler = require('express-async-handler');
const nodemailer = require('nodemailer');

// @desc    Submit contact form
// @route   POST /api/contact
// @access  Public
const submitContactForm = asyncHandler(async (req, res) => {
  const { name, email, message } = req.body;
  
  // Validate input
  if (!name || !email || !message) {
    res.status(400);
    throw new Error('Please fill in all fields');
  }
  
  try {
    // For testing purposes, let's skip the actual email sending
    // and just return success
    
    // Log the form data for debugging
    console.log('Contact form submission:', { name, email, message });
    
    // Skip email sending for now
    // When you're ready to implement email sending, uncomment this section
    // and replace with your actual SMTP credentials
    /*
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    
    await transporter.sendMail({
      from: `"Pet Rescue Website" <${process.env.EMAIL_USER}>`,
      to: '<EMAIL>',
      replyTo: email,
      subject: `New Contact Form Submission from ${name}`,
      text: `Name: ${name}\nEmail: ${email}\nMessage: ${message}`,
      html: `<p><strong>Name:</strong> ${name}</p>
             <p><strong>Email:</strong> ${email}</p>
             <p><strong>Message:</strong> ${message}</p>`
    });
    */
    
    res.status(200).json({ message: 'Your message has been sent successfully!' });
  } catch (error) {
    console.error('Contact form error:', error);
    res.status(500);
    throw new Error('Failed to send message. Please try again later.');
  }
});

module.exports = { submitContactForm };


