const mongoose = require('mongoose');

const partnerSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    logoUrl: {
      type: String,
      required: true
    },
    websiteUrl: {
      type: String
    },
    partnerType: {
      type: String,
      enum: ['sponsor', 'shelter', 'veterinary', 'supplier', 'other'],
      default: 'other'
    },
    featured: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Partner', partnerSchema);