const mongoose = require('mongoose');

const adoptionSchema = mongoose.Schema(
  {
    petId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Pet'
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'cancelled'],
      default: 'pending'
    },
    reason: {
      type: String,
      required: true
    },
    homeType: {
      type: String,
      required: true
    },
    hasChildren: {
      type: Boolean,
      default: false
    },
    hasOtherPets: {
      type: Boolean,
      default: false
    },
    otherPets: {
      type: String
    },
    notes: {
      type: String
    },
    adminNotes: {
      type: String
    },
    requestDate: {
      type: Date,
      default: Date.now
    },
    responseDate: {
      type: Date
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Adoption', adoptionSchema);
