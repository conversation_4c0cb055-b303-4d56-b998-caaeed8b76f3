const express = require('express');
const router = express.Router();
const {
  getPartners,
  createPartner,
  updatePartner,
  deletePartner
} = require('../controllers/partnerController');
const { protect, admin } = require('../middleware/authMiddleware');

// Public routes
router.get('/', getPartners);

// Admin routes
router.post('/', protect, admin, createPartner);
router.put('/:id', protect, admin, updatePartner);
router.delete('/:id', protect, admin, deletePartner);

module.exports = router;