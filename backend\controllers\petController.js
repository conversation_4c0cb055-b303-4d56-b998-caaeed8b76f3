const Pet = require('../models/Pet');
const asyncHandler = require('express-async-handler');

// @desc    Get all pets
// @route   GET /api/pets
// @access  Public
const getPets = asyncHandler(async (req, res) => {
  const { type, status, size } = req.query;
  
  // Build filter object
  const filter = {};
  if (type) filter.type = type;
  if (status) filter.adoptionStatus = status;
  if (size) filter.size = size;
  
  const pets = await Pet.find(filter).sort({ createdAt: -1 });
  res.status(200).json(pets);
});

// @desc    Get single pet
// @route   GET /api/pets/:id
// @access  Public
const getPet = asyncHandler(async (req, res) => {
  console.log(`Fetching pet with ID: ${req.params.id}`);
  
  // Validate if the ID is a valid MongoDB ObjectId
  if (!req.params.id.match(/^[0-9a-fA-F]{24}$/)) {
    console.log('Invalid pet ID format');
    res.status(400);
    throw new Error('Invalid pet ID format');
  }

  try {
    const pet = await Pet.findById(req.params.id);
    
    if (!pet) {
      console.log(`Pet with ID ${req.params.id} not found`);
      res.status(404);
      throw new Error('Pet not found');
    }
    
    console.log(`Pet found: ${pet.name}`);
    res.status(200).json(pet);
  } catch (error) {
    console.error(`Error fetching pet: ${error.message}`);
    res.status(error.statusCode || 500);
    throw new Error(error.message || 'Server error');
  }
});

// @desc    Create a pet
// @route   POST /api/pets
// @access  Private
const createPet = asyncHandler(async (req, res) => {
  const { 
    name, 
    type, 
    breed, 
    age, 
    gender, 
    size, 
    description, 
    healthStatus, 
    vaccinated, 
    neutered, 
    imageUrl 
  } = req.body;

  if (!name || !type || !breed || !age || !description) {
    res.status(400);
    throw new Error('Please provide all required fields');
  }

  const pet = await Pet.create({
    name,
    type,
    breed,
    age,
    gender,
    size,
    description,
    healthStatus,
    vaccinated,
    neutered,
    imageUrl,
    createdBy: req.user.id
  });

  res.status(201).json(pet);
});

// @desc    Update a pet
// @route   PUT /api/pets/:id
// @access  Private
const updatePet = asyncHandler(async (req, res) => {
  const pet = await Pet.findById(req.params.id);
  
  if (!pet) {
    res.status(404);
    throw new Error('Pet not found');
  }
  
  // Check if user is authorized to update this pet
  if (pet.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to update this pet');
  }
  
  const updatedPet = await Pet.findByIdAndUpdate(
    req.params.id, 
    req.body, 
    { new: true, runValidators: true }
  );
  
  res.status(200).json(updatedPet);
});

// @desc    Delete a pet
// @route   DELETE /api/pets/:id
// @access  Private
const deletePet = asyncHandler(async (req, res) => {
  const pet = await Pet.findById(req.params.id);
  
  if (!pet) {
    res.status(404);
    throw new Error('Pet not found');
  }
  
  // Check if user is authorized to delete this pet
  if (pet.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to delete this pet');
  }
  
  await pet.deleteOne();
  
  res.status(200).json({ id: req.params.id });
});

// @desc    Update pet adoption status
// @route   PATCH /api/pets/:id/status
// @access  Private
const updatePetStatus = asyncHandler(async (req, res) => {
  console.log(`Updating pet status for ID: ${req.params.id}`);
  const { adoptionStatus } = req.body;
  
  if (!adoptionStatus) {
    res.status(400);
    throw new Error('Please provide adoption status');
  }
  
  const pet = await Pet.findById(req.params.id);
  
  if (!pet) {
    res.status(404);
    throw new Error('Pet not found');
  }
  
  // Allow any authenticated user to request adoption (change status to 'pending')
  // But only admins and creators can set other statuses
  if (adoptionStatus === 'pending') {
    console.log(`User ${req.user.id} is requesting adoption for pet ${pet.name}`);
    // Any authenticated user can request adoption
  } else if (pet.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
    // Only admins and creators can set other statuses
    res.status(401);
    throw new Error('Not authorized to update this pet status');
  }
  
  pet.adoptionStatus = adoptionStatus;
  const updatedPet = await pet.save();
  console.log(`Pet status updated to: ${adoptionStatus}`);
  
  res.status(200).json(updatedPet);
});

module.exports = {
  getPets,
  getPet,
  createPet,
  updatePet,
  deletePet,
  updatePetStatus
};
