const express = require('express');
const router = express.Router();
const {
  createDonation,
  getDonationStats,
  getTestimonials
} = require('../controllers/donationController');
const { protect, optionalAuth } = require('../middleware/authMiddleware');

// Public routes
router.get('/stats', getDonationStats);
router.get('/testimonials', getTestimonials);

// Optional auth - works for both logged in and anonymous users
router.post('/', optionalAuth, createDonation);

module.exports = router;