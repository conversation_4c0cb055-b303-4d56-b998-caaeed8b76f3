const asyncHandler = require('express-async-handler');
const Event = require('../models/Event');

// @desc    Get all events
// @route   GET /api/events
// @access  Public
const getEvents = asyncHandler(async (req, res) => {
  const { type, upcoming } = req.query;
  
  // Build filter object
  const filter = {};
  if (type) filter.eventType = type;
  
  // Filter for upcoming events
  if (upcoming === 'true') {
    filter.date = { $gte: new Date() };
  }
  
  const events = await Event.find(filter).sort({ date: 1 });
  res.status(200).json(events);
});

// @desc    Get event by ID
// @route   GET /api/events/:id
// @access  Public
const getEventById = asyncHandler(async (req, res) => {
  const event = await Event.findById(req.params.id);
  
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }
  
  res.status(200).json(event);
});

// @desc    Create an event
// @route   POST /api/events
// @access  Private/Admin
const createEvent = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    date,
    time,
    location,
    imageUrl,
    eventType,
    isVirtual,
    virtualLink,
    registrationRequired,
    registrationLink
  } = req.body;

  if (!title || !description || !date || !time || !location) {
    res.status(400);
    throw new Error('Please provide all required fields');
  }

  const event = await Event.create({
    title,
    description,
    date: new Date(date),
    time,
    location,
    imageUrl,
    eventType: eventType || 'other',
    isVirtual: isVirtual || false,
    virtualLink,
    registrationRequired: registrationRequired || false,
    registrationLink,
    createdBy: req.user.id
  });

  res.status(201).json(event);
});

// @desc    Update an event
// @route   PUT /api/events/:id
// @access  Private/Admin
const updateEvent = asyncHandler(async (req, res) => {
  const event = await Event.findById(req.params.id);
  
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }
  
  const updatedEvent = await Event.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  );
  
  res.status(200).json(updatedEvent);
});

// @desc    Delete an event
// @route   DELETE /api/events/:id
// @access  Private/Admin
const deleteEvent = asyncHandler(async (req, res) => {
  const event = await Event.findById(req.params.id);
  
  if (!event) {
    res.status(404);
    throw new Error('Event not found');
  }
  
  await event.deleteOne();
  
  res.status(200).json({ message: 'Event removed' });
});

module.exports = {
  getEvents,
  getEventById,
  createEvent,
  updateEvent,
  deleteEvent
};