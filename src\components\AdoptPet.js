// File: src/components/AdoptPet.js
import React, { useState, useEffect, useCallback } from 'react';
import {
  PawPrint,
  CalendarCheck,
  FileText,
  HeartHandshake,
  ShieldCheck,
  SmilePlus,
  Search,
  Filter,
  Heart,
  Star,
  Clock,
  CheckCircle,
  Home,
  Clipboard,
  ArrowRight,
  MessageCircle,
  User,
  BookmarkPlus,
  Bookmark,
  AlertCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  X
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import Navigation from './Navigation';
import petService from '../services/petService';
import favoriteService from '../services/favoriteService';
import adoptionService from '../services/adoptionService';
import './AdoptPet.css';
import errorService, { NetworkError, ValidationError } from '../services/errorService';

const AdoptPet = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [breed, setBreed] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [age, setAge] = useState('');
  const [species, setSpecies] = useState('');
  const [size, setSize] = useState('');
  const [gender, setGender] = useState('');
  const [meetingTime, setMeetingTime] = useState('');
  const [meetingDate, setMeetingDate] = useState('');
  const [meetingNotes, setMeetingNotes] = useState('');
  const [includeSpecialNeeds, setIncludeSpecialNeeds] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [favorites, setFavorites] = useState([]);
  const [showFavorites, setShowFavorites] = useState(false);
  const [showAdoptionProcess, setShowAdoptionProcess] = useState(false);
  const [showSuccessStories, setShowSuccessStories] = useState(false);
  const [showMeetingForm, setShowMeetingForm] = useState(false);
  const [selectedPetForMeeting, setSelectedPetForMeeting] = useState(null);
  
  const [pets, setPets] = useState([]);
  const [filteredPets, setFilteredPets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingFavorites, setLoadingFavorites] = useState(true);
  const [loadingAdoptionSteps, setLoadingAdoptionSteps] = useState(true);
  const [loadingSuccessStories, setLoadingSuccessStories] = useState(true);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [formErrors, setFormErrors] = useState({});
  
  // Default adoption steps and success stories (used as fallback when API fails)
  const [adoptionProcessSteps, setAdoptionProcessSteps] = useState([
    {
      icon: <Search size={24} color="#a68fbd" />,
      title: "Find Your Perfect Match",
      description: "Browse our available pets and use filters to find one that matches your lifestyle."
    },
    {
      icon: <CalendarCheck size={24} color="#a68fbd" />,
      title: "Schedule a Meet & Greet",
      description: "Spend time with your potential new family member to ensure it's a good match."
    },
    {
      icon: <Clipboard size={24} color="#a68fbd" />,
      title: "Complete Application",
      description: "Fill out our adoption application with your information and references."
    },
    {
      icon: <Home size={24} color="#a68fbd" />,
      title: "Home Check",
      description: "Our team will verify your home is safe and suitable for your new pet."
    },
    {
      icon: <CheckCircle size={24} color="#a68fbd" />,
      title: "Adoption Approval",
      description: "Once approved, you'll pay the adoption fee and sign the adoption contract."
    },
    {
      icon: <HeartHandshake size={24} color="#a68fbd" />,
      title: "Welcome Home",
      description: "Take your new family member home and begin your journey together!"
    }
  ]);
  
  const [adoptionSuccessStories, setAdoptionSuccessStories] = useState([
    {
      petName: "Max",
      petType: "Dog",
      ownerName: "Sarah Johnson",
      image: "https://images.unsplash.com/photo-1561037404-61cd46aa615b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      story: "Max was a shy rescue who had been overlooked for months. When I met him, we connected instantly. Now he's the most confident, loving companion I could ask for!",
      adoptedDate: "6 months ago"
    },
    {
      petName: "Luna",
      petType: "Cat",
      ownerName: "Michael Chen",
      image: "https://images.unsplash.com/photo-1533743983669-94fa5c4338ec?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      story: "Luna was found as a stray with her kittens. After her babies were adopted, she stayed behind until I came along. She's now the queen of my apartment and my heart.",
      adoptedDate: "1 year ago"
    },
    {
      petName: "Coco",
      petType: "Rabbit",
      ownerName: "Emily Rodriguez",
      image: "https://images.unsplash.com/photo-1585110396000-c9ffd4e4b308?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
      story: "I never thought I'd adopt a rabbit, but Coco stole my heart during a visit to the shelter. She's incredibly smart and has become the most delightful companion.",
      adoptedDate: "8 months ago"
    }
  ]);

  // Monitor online status
  useEffect(() => {
    const handleOnlineStatusChange = (status) => {
      setIsOnline(status);
      if (status) {
        // When coming back online, sync any pending data
        syncPendingData();
      }
    };

    // Add listener for online status changes
    const unsubscribe = errorService.addOnlineStatusListener(handleOnlineStatusChange);
    
    // Initial check
    setIsOnline(errorService.isOnline());
    
    return () => {
      // Clean up listener when component unmounts
      unsubscribe();
    };
  }, []);

  // Sync pending data when coming back online
  const syncPendingData = useCallback(async () => {
    try {
      // Sync adoption-related pending data
      await adoptionService.syncPendingItems();
      
      // Refresh data after syncing
      fetchPets();
      fetchFavorites();
    } catch (error) {
      console.error('Error syncing pending data:', error);
    }
  }, []);

  // Fetch pets
  const fetchPets = useCallback(async () => {
    try {
      setLoading(true);
      const data = await petService.getAllPets({ status: 'available' });
      setPets(data);
      setFilteredPets(data);
      setError(null);
    } catch (err) {
      const handledError = errorService.handleApiError(err);
      console.error('Error fetching pets:', handledError);
      
      if (handledError instanceof NetworkError) {
        setError('Unable to connect to the server. Please check your internet connection.');
      } else {
        setError('Failed to load pets. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch favorites
  const fetchFavorites = useCallback(async () => {
    try {
      setLoadingFavorites(true);
      const favoritePets = await favoriteService.getFavorites();
      setFavorites(favoritePets);
    } catch (err) {
      console.error('Error fetching favorites:', err);
      // Don't show error for favorites, just use empty array
      setFavorites([]);
    } finally {
      setLoadingFavorites(false);
    }
  }, []);

  // Fetch adoption steps
  const fetchAdoptionSteps = useCallback(async () => {
    try {
      setLoadingAdoptionSteps(true);
      const steps = await adoptionService.getAdoptionProcess();
      setAdoptionProcessSteps(steps);
    } catch (err) {
      console.error('Error fetching adoption steps:', err);
      // Use default steps from the component
    } finally {
      setLoadingAdoptionSteps(false);
    }
  }, []);

  // Fetch success stories
  const fetchSuccessStories = useCallback(async () => {
    try {
      setLoadingSuccessStories(true);
      const stories = await adoptionService.getSuccessStories();
      setAdoptionSuccessStories(stories);
    } catch (err) {
      console.error('Error fetching success stories:', err);
      // Use default stories from the component
    } finally {
      setLoadingSuccessStories(false);
    }
  }, []);

  // Initial data fetching
  useEffect(() => {
    fetchPets();
    fetchFavorites();
    fetchAdoptionSteps();
    fetchSuccessStories();
  }, [fetchPets, fetchFavorites, fetchAdoptionSteps, fetchSuccessStories]);

  const handleSearch = (e) => {
    e.preventDefault();
    applyFilters();
  };

  const handleViewPetDetails = (petId) => {
    navigate(`/pet-details/${petId}`);
  };
  
  const handleClearFilters = () => {
    setSearchTerm('');
    setBreed('');
    setAge('');
    setSpecies('');
    setSize('');
    setGender('');
    setIncludeSpecialNeeds(false);
  };
  
  const toggleFavorite = async (petId) => {
    try {
      if (favorites.includes(petId)) {
        // Optimistically update UI
        setFavorites(favorites.filter((id) => id !== petId));
        // Then update backend
        await favoriteService.removeFavorite(petId);
      } else {
        // Optimistically update UI
        setFavorites([...favorites, petId]);
        // Then update backend
        await favoriteService.addFavorite(petId);
      }
    } catch (err) {
      // If there's an error, refresh favorites to ensure UI is in sync with backend
      console.error('Error toggling favorite:', err);
      fetchFavorites();
    }
  };
  
  const toggleShowFavorites = () => {
    setShowFavorites(!showFavorites);
    applyFilters();
  };
  
  // Apply filters when any filter criteria changes
  useEffect(() => {
    applyFilters();
  }, [pets, searchTerm, breed, species, age, size, gender, includeSpecialNeeds, showFavorites, favorites]);
  
  // Function to apply all filters
  const applyFilters = () => {
    if (!pets.length) return;
    
    let results = [...pets];
    
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      results = results.filter(pet => 
        pet.name.toLowerCase().includes(term) || 
        pet.breed.toLowerCase().includes(term) || 
        (pet.description && pet.description.toLowerCase().includes(term))
      );
    }
    
    if (breed) {
      results = results.filter(pet => 
        pet.breed.toLowerCase().includes(breed.toLowerCase())
      );
    }
    
    if (species) {
      results = results.filter(pet => 
        pet.type && pet.type.toLowerCase() === species.toLowerCase()
      );
    }
    
    if (age) {
      results = results.filter(pet => {
        if (age === 'Puppy/Kitten') return pet.age < 1;
        if (age === 'Young') return pet.age >= 1 && pet.age <= 3;
        if (age === 'Adult') return pet.age > 3 && pet.age <= 8;
        if (age === 'Senior') return pet.age > 8;
        return true;
      });
    }
    
    if (size) {
      results = results.filter(pet => 
        pet.size && pet.size.toLowerCase() === size.toLowerCase()
      );
    }
    
    if (gender) {
      results = results.filter(pet => 
        pet.gender && pet.gender.toLowerCase() === gender.toLowerCase()
      );
    }
    
    if (includeSpecialNeeds) {
      results = results.filter(pet => 
        pet.specialNeeds === true || pet.healthStatus !== 'healthy'
      );
    }
    
    if (showFavorites) {
      results = results.filter(pet => favorites.includes(pet._id));
    }
    
    setFilteredPets(results);
  };
  
  // Schedule a meet & greet with a pet
  const scheduleMeeting = async (e) => {
    e.preventDefault();
    
    // Form validation
    const errors = {};
    if (!name) errors.name = 'Name is required';
    if (!email) errors.email = 'Email is required';
    if (!phone) errors.phone = 'Phone number is required';
    if (!meetingDate) errors.meetingDate = 'Date is required';
    if (!meetingTime) errors.meetingTime = 'Time is required';
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    try {
      // Clear previous errors
      setFormErrors({});
      
      const meetingData = {
        petId: selectedPetForMeeting._id,
        petName: selectedPetForMeeting.name,
        name,
        email,
        phone,
        date: meetingDate,
        time: meetingTime,
        notes: meetingNotes
      };
      
      await adoptionService.scheduleMeeting(meetingData);
      
      // Reset form
      setName('');
      setEmail('');
      setPhone('');
      setMeetingDate('');
      setMeetingTime('');
      setMeetingNotes('');
      setSelectedPetForMeeting(null);
      setShowMeetingForm(false);
      
      // Show success message
      alert(`Meeting scheduled successfully to meet ${selectedPetForMeeting.name}!`);
    } catch (err) {
      const handledError = errorService.handleApiError(err);
      console.error('Error scheduling meeting:', handledError);
      
      if (handledError instanceof ValidationError) {
        setFormErrors(errorService.formatValidationErrors(handledError));
      } else if (handledError instanceof NetworkError) {
        alert('Your meeting request has been saved locally and will be submitted when you\'re back online.');
        setShowMeetingForm(false);
      } else {
        alert('Failed to schedule meeting. Please try again later.');
      }
    }
  };
  
  // Open meeting form for a specific pet
  const openMeetingForm = (pet) => {
    setSelectedPetForMeeting(pet);
    setShowMeetingForm(true);
  };

  // Render pet card
  const renderPetCard = (pet) => {
    const isFavorited = favorites.includes(pet._id);

    return (
      <motion.div
        key={pet._id}
        className="pet-card"
        whileHover={{ y: -8 }}
        onClick={() => handleViewPetDetails(pet._id)}
      >
        {/* Special needs tag */}
        {pet.specialNeeds && (
          <div className="special-needs-badge">
            Special Needs
          </div>
        )}

        {/* Card actions */}
        <div className="pet-card-actions">
          <button
            className="favorite-button"
            onClick={(e) => {
              e.stopPropagation();
              toggleFavorite(pet._id);
            }}
          >
            {isFavorited ? (
              <Heart size={20} fill="#ef4444" color="#ef4444" />
            ) : (
              <Heart size={20} color="#9ca3af" />
            )}
          </button>

          <button
            className="meet-greet-button"
            onClick={(e) => {
              e.stopPropagation();
              openMeetingForm(pet);
            }}
          >
            <CalendarCheck size={14} />
            Meet & Greet
          </button>
        </div>

        <div className="pet-card-image">
          <img
            src={pet.image || 'https://via.placeholder.com/300x200?text=No+Image'}
            alt={pet.name}
          />
          <div className="pet-card-overlay">
            <h3 className="pet-name">{pet.name}</h3>
            <p className="pet-breed">{pet.breed}</p>
          </div>
        </div>

        <div className="pet-card-content">
          <div className="pet-info-row">
            <span className="pet-age">{pet.age} {pet.age === 1 ? 'year' : 'years'} old</span>
            <span className={`pet-gender-badge ${pet.gender === 'Male' ? 'pet-gender-male' : 'pet-gender-female'}`}>
              {pet.gender}
            </span>
          </div>

          <div className="pet-info-row">
            <span className="pet-details">{pet.size} • {pet.species || pet.type}</span>
            <span className={`pet-status-badge ${pet.status === 'available' ? 'pet-status-available' : 'pet-status-unavailable'}`}>
              {pet.status === 'available' ? 'Available' : 'Not Available'}
            </span>
          </div>
        </div>
      </motion.div>
    );
  };

  // Render meeting form
  const renderMeetingForm = () => {
    if (!showMeetingForm || !selectedPetForMeeting) return null;

    return (
      <div className="fixed inset-0 modal-overlay flex items-center justify-center z-50">
        <div className="modal-content">
          <div className="modal-header">
            <h2 className="modal-title">Schedule a Meet & Greet</h2>
            <button
              onClick={() => setShowMeetingForm(false)}
              className="modal-close-button"
            >
              <X size={20} />
            </button>
          </div>

          <div className="modal-pet-info">
            <img
              src={selectedPetForMeeting.image || 'https://via.placeholder.com/100?text=No+Image'}
              alt={selectedPetForMeeting.name}
              className="modal-pet-image"
            />
            <div>
              <h3 className="font-semibold text-lg">{selectedPetForMeeting.name}</h3>
              <p className="text-sm text-gray-600">{selectedPetForMeeting.breed} • {selectedPetForMeeting.age} {selectedPetForMeeting.age === 1 ? 'year' : 'years'} old</p>
            </div>
          </div>

          <form onSubmit={scheduleMeeting}>
            <div className="modal-form-group">
              <label className="modal-form-label">Your Name</label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className={`modal-form-input ${formErrors.name ? 'border-red-500' : ''}`}
                placeholder="John Doe"
              />
              {formErrors.name && <p className="modal-form-error">{formErrors.name}</p>}
            </div>

            <div className="modal-form-group">
              <label className="modal-form-label">Email</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`modal-form-input ${formErrors.email ? 'border-red-500' : ''}`}
                placeholder="<EMAIL>"
              />
              {formErrors.email && <p className="modal-form-error">{formErrors.email}</p>}
            </div>

            <div className="modal-form-group">
              <label className="modal-form-label">Phone Number</label>
              <input
                type="tel"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className={`modal-form-input ${formErrors.phone ? 'border-red-500' : ''}`}
                placeholder="(*************"
              />
              {formErrors.phone && <p className="modal-form-error">{formErrors.phone}</p>}
            </div>

            <div className="grid grid-cols-2 gap-4 modal-form-group">
              <div>
                <label className="modal-form-label">Date</label>
                <input
                  type="date"
                  value={meetingDate}
                  onChange={(e) => setMeetingDate(e.target.value)}
                  className={`modal-form-input ${formErrors.meetingDate ? 'border-red-500' : ''}`}
                  min={new Date().toISOString().split('T')[0]}
                />
                {formErrors.meetingDate && <p className="modal-form-error">{formErrors.meetingDate}</p>}
              </div>

              <div>
                <label className="modal-form-label">Time</label>
                <input
                  type="time"
                  value={meetingTime}
                  onChange={(e) => setMeetingTime(e.target.value)}
                  className={`modal-form-input ${formErrors.meetingTime ? 'border-red-500' : ''}`}
                />
                {formErrors.meetingTime && <p className="modal-form-error">{formErrors.meetingTime}</p>}
              </div>
            </div>

            <div className="modal-form-group">
              <label className="modal-form-label">Additional Notes (Optional)</label>
              <textarea
                value={meetingNotes}
                onChange={(e) => setMeetingNotes(e.target.value)}
                className="modal-form-textarea"
                rows="3"
                placeholder="Any questions or special requests..."
              ></textarea>
            </div>

            <div className="modal-form-actions">
              <button
                type="button"
                onClick={() => setShowMeetingForm(false)}
                className="modal-button-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="modal-button-primary"
              >
                Schedule Meeting
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Render offline status indicator
  const renderOfflineIndicator = () => {
    if (isOnline) return null;

    return (
      <div className="offline-indicator">
        <WifiOff size={18} />
        <span>You're offline. Some features may be limited.</span>
      </div>
    );
  };

  return (
    <div className="adopt-pet-container">
      <Navigation />

      {/* Offline indicator */}
      {renderOfflineIndicator()}

      {/* Meeting form modal */}
      {renderMeetingForm()}

      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="adopt-pet-header">
          <h1 className="adopt-pet-title">Find Your Perfect Companion</h1>
          <p className="adopt-pet-subtitle">Browse our available pets and find your new best friend</p>

          <div className="action-buttons-container">
            <button
              onClick={toggleShowFavorites}
              className={`action-button ${showFavorites ? 'favorites-active' : ''}`}
            >
              <Heart size={18} className={showFavorites ? 'fill-current' : ''} />
              {showFavorites ? 'All Pets' : 'Favorites'}
            </button>

            <button
              onClick={() => setShowAdoptionProcess(!showAdoptionProcess)}
              className="action-button"
            >
              <Clipboard size={18} />
              Adoption Process
            </button>
          </div>
        </div>

        {/* Search and filter section */}
        <div className="search-filter-section">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-2 md:mb-0">Search & Filters</h2>
            <button
              onClick={handleClearFilters}
              className="text-purple-600 text-sm font-medium hover:text-purple-800 transition-colors"
            >
              Clear All Filters
            </button>
          </div>

          <form onSubmit={handleSearch}>
            <div className="search-input-container">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, breed, or description..."
                className="search-input"
              />
              <Search size={18} className="search-icon" />
            </div>
            <div className="text-center">
              <button type="submit" className="search-button">
                <Search size={18} className="mr-2" />
                Search Pets
              </button>
            </div>
          </form>

          <div className="filter-grid">
            <div>
              <label className="filter-label">Species</label>
              <select
                value={species}
                onChange={(e) => setSpecies(e.target.value)}
                className="filter-select"
              >
                <option value="">All Species</option>
                <option value="dog">Dogs</option>
                <option value="cat">Cats</option>
                <option value="rabbit">Rabbits</option>
                <option value="bird">Birds</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label className="filter-label">Breed</label>
              <input
                type="text"
                value={breed}
                onChange={(e) => setBreed(e.target.value)}
                placeholder="Enter breed"
                className="filter-input"
              />
            </div>

            <div>
              <label className="filter-label">Age</label>
              <select
                value={age}
                onChange={(e) => setAge(e.target.value)}
                className="filter-select"
              >
                <option value="">All Ages</option>
                <option value="Puppy/Kitten">Puppy/Kitten</option>
                <option value="Young">Young</option>
                <option value="Adult">Adult</option>
                <option value="Senior">Senior</option>
              </select>
            </div>

            <div>
              <label className="filter-label">Size</label>
              <select
                value={size}
                onChange={(e) => setSize(e.target.value)}
                className="filter-select"
              >
                <option value="">All Sizes</option>
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </select>
            </div>
          </div>

          <div className="mt-6 flex items-center justify-center">
            <input
              type="checkbox"
              id="includeSpecialNeeds"
              checked={includeSpecialNeeds}
              onChange={(e) => setIncludeSpecialNeeds(e.target.checked)}
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded mr-2"
            />
            <label htmlFor="includeSpecialNeeds" className="text-sm text-gray-700 font-medium">
              Show pets with special needs
            </label>
          </div>
        </div>
        
        {/* Adoption process section */}
        <AnimatePresence>
          {showAdoptionProcess && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="search-filter-section mb-8 overflow-hidden"
            >
              <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">Our Adoption Process</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {adoptionProcessSteps.map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-6 border-2 border-purple-100 rounded-2xl bg-gradient-to-br from-purple-50 to-white flex flex-col items-center text-center hover:shadow-lg transition-all duration-300"
                  >
                    <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-4 rounded-full mb-4 shadow-lg">
                      <div className="text-white">
                        {step.icon}
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">{step.title}</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">{step.description}</p>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 text-center">
                <button
                  onClick={() => {
                    setShowAdoptionProcess(false);
                    setShowSuccessStories(true);
                  }}
                  className="action-button"
                >
                  See Adoption Success Stories <ArrowRight size={16} className="ml-2" />
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Success stories section */}
        <AnimatePresence>
          {showSuccessStories && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="search-filter-section mb-8 overflow-hidden"
            >
              <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">Happy Adoption Stories</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {adoptionSuccessStories.map((story, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="pet-card"
                  >
                    <div className="pet-card-image">
                      <img
                        src={story.image}
                        alt={`${story.petName} with ${story.ownerName}`}
                      />
                      <div className="pet-card-overlay">
                        <h3 className="pet-name">{story.petName}</h3>
                        <p className="pet-breed">Adopted {story.adoptedDate}</p>
                      </div>
                    </div>
                    <div className="pet-card-content">
                      <p className="text-gray-600 text-sm mb-3 leading-relaxed">{story.story}</p>
                      <p className="text-sm font-semibold text-purple-600">- {story.ownerName}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              <div className="mt-8 text-center">
                <button
                  onClick={() => setShowSuccessStories(false)}
                  className="action-button"
                >
                  <X size={16} className="mr-2" />
                  Close Success Stories
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Pet listings */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            {showFavorites ? 'Your Favorite Pets' : 'Available Pets'}
          </h2>
          
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p className="text-gray-600 text-lg">Loading amazing pets for you...</p>
            </div>
          ) : error ? (
            <div className="error-container">
              <div className="flex items-start">
                <AlertCircle size={24} className="mr-3 mt-1 flex-shrink-0" />
                <div>
                  <p className="font-semibold text-lg mb-2">Oops! Something went wrong</p>
                  <p className="text-sm mb-3">{error}</p>
                  <button
                    onClick={fetchPets}
                    className="action-button text-sm"
                  >
                    <RefreshCw size={16} className="mr-2" /> Try Again
                  </button>
                </div>
              </div>
            </div>
          ) : filteredPets.length === 0 ? (
            <div className="no-pets-container">
              <div className="text-gray-400 mb-6">
                <Search size={64} className="mx-auto" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-700 mb-3">No pets found</h3>
              <p className="text-gray-600 mb-6 text-lg">
                {showFavorites
                  ? "You haven't added any pets to your favorites yet. Start browsing to find your perfect companion!"
                  : "No pets match your current filters. Try adjusting your search criteria."}
              </p>
              {showFavorites && (
                <button
                  onClick={() => setShowFavorites(false)}
                  className="action-button"
                >
                  <Heart size={18} className="mr-2" />
                  Browse All Pets
                </button>
              )}
              {!showFavorites && (
                <button
                  onClick={handleClearFilters}
                  className="action-button"
                >
                  <RefreshCw size={18} className="mr-2" />
                  Clear Filters
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {filteredPets.map(pet => renderPetCard(pet))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdoptPet;