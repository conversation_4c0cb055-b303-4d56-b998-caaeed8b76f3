const asyncHandler = require('express-async-handler');
const Partner = require('../models/Partner');

// @desc    Get all partners
// @route   GET /api/partners
// @access  Public
const getPartners = asyncHandler(async (req, res) => {
  const { type, featured } = req.query;
  
  // Build filter object
  const filter = { isActive: true };
  if (type) filter.partnerType = type;
  if (featured === 'true') filter.featured = true;
  
  const partners = await Partner.find(filter).sort({ order: 1 });
  res.status(200).json(partners);
});

// @desc    Create a partner
// @route   POST /api/partners
// @access  Private/Admin
const createPartner = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    logoUrl,
    websiteUrl,
    partnerType,
    featured,
    order
  } = req.body;

  if (!name || !description || !logoUrl) {
    res.status(400);
    throw new Error('Please provide all required fields');
  }

  const partner = await Partner.create({
    name,
    description,
    logoUrl,
    websiteUrl,
    partnerType: partnerType || 'other',
    featured: featured || false,
    order: order || 0,
    isActive: true
  });

  res.status(201).json(partner);
});

// @desc    Update a partner
// @route   PUT /api/partners/:id
// @access  Private/Admin
const updatePartner = asyncHandler(async (req, res) => {
  const partner = await Partner.findById(req.params.id);
  
  if (!partner) {
    res.status(404);
    throw new Error('Partner not found');
  }
  
  const updatedPartner = await Partner.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  );
  
  res.status(200).json(updatedPartner);
});

// @desc    Delete a partner
// @route   DELETE /api/partners/:id
// @access  Private/Admin
const deletePartner = asyncHandler(async (req, res) => {
  const partner = await Partner.findById(req.params.id);
  
  if (!partner) {
    res.status(404);
    throw new Error('Partner not found');
  }
  
  // Soft delete - just mark as inactive
  partner.isActive = false;
  await partner.save();
  
  res.status(200).json({ message: 'Partner removed' });
});

module.exports = {
  getPartners,
  createPartner,
  updatePartner,
  deletePartner
};
