const asyncHandler = require('express-async-handler');
const Favorite = require('../models/Favorite');
const Pet = require('../models/Pet');

// @desc    Get user's favorites
// @route   GET /api/favorites
// @access  Private
const getFavorites = asyncHandler(async (req, res) => {
  const favorites = await Favorite.find({ userId: req.user.id });
  
  // Extract just the pet IDs for the frontend
  const petIds = favorites.map(favorite => favorite.petId);
  
  res.status(200).json(petIds);
});

// @desc    Add a pet to favorites
// @route   POST /api/favorites
// @access  Private
const addFavorite = asyncHandler(async (req, res) => {
  const { petId } = req.body;
  
  if (!petId) {
    res.status(400);
    throw new Error('Pet ID is required');
  }
  
  // Check if pet exists
  const pet = await Pet.findById(petId);
  if (!pet) {
    res.status(404);
    throw new Error('Pet not found');
  }
  
  // Check if already favorited
  const existingFavorite = await Favorite.findOne({
    userId: req.user.id,
    petId
  });
  
  if (existingFavorite) {
    res.status(400);
    throw new Error('Pet is already in favorites');
  }
  
  // Add to favorites
  const favorite = await Favorite.create({
    userId: req.user.id,
    petId
  });
  
  // Get all favorites to return to frontend
  const favorites = await Favorite.find({ userId: req.user.id });
  const allPetIds = favorites.map(fav => fav.petId);
  
  res.status(201).json({ success: true, favorites: allPetIds });
});

// @desc    Remove a pet from favorites
// @route   DELETE /api/favorites/:id
// @access  Private
const removeFavorite = asyncHandler(async (req, res) => {
  const petId = req.params.id;
  
  // Check if favorite exists
  const favorite = await Favorite.findOne({
    userId: req.user.id,
    petId
  });
  
  if (!favorite) {
    res.status(404);
    throw new Error('Favorite not found');
  }
  
  // Remove from favorites
  await favorite.deleteOne();
  
  // Get updated favorites list
  const favorites = await Favorite.find({ userId: req.user.id });
  const updatedPetIds = favorites.map(fav => fav.petId);
  
  res.status(200).json({ success: true, favorites: updatedPetIds });
});

module.exports = {
  getFavorites,
  addFavorite,
  removeFavorite
};