const Adoption = require('../models/Adoption');
const Pet = require('../models/Pet');
const asyncHandler = require('express-async-handler');

// @desc    Request to adopt a pet
// @route   POST /api/pets/:id/adopt
// @access  Private
const requestAdoption = asyncHandler(async (req, res) => {
  const petId = req.params.id;
  const userId = req.user.id;
  
  // Validate if the pet exists
  const pet = await Pet.findById(petId);
  if (!pet) {
    res.status(404);
    throw new Error('Pet not found');
  }
  
  // Check if the pet is available for adoption
  if (pet.adoptionStatus !== 'available') {
    res.status(400);
    throw new Error('This pet is not available for adoption');
  }
  
  // Check if the user already has a pending adoption request for this pet
  const existingRequest = await Adoption.findOne({ 
    petId, 
    userId, 
    status: 'pending' 
  });
  
  if (existingRequest) {
    res.status(400);
    throw new Error('You already have a pending adoption request for this pet');
  }
  
  // Extract adoption request details
  const { 
    reason, 
    homeType, 
    hasChildren, 
    hasOtherPets, 
    otherPets 
  } = req.body;
  
  // Create adoption request
  const adoptionRequest = await Adoption.create({
    petId,
    userId,
    reason: reason || 'No reason provided',
    homeType: homeType || 'Not specified',
    hasChildren: hasChildren || false,
    hasOtherPets: hasOtherPets || false,
    otherPets: otherPets || '',
    requestDate: new Date()
  });
  
  // Update pet status to pending
  pet.adoptionStatus = 'pending';
  await pet.save();
  
  res.status(201).json(adoptionRequest);
});

// @desc    Get all adoption requests (admin only)
// @route   GET /api/pets/adoptions
// @access  Private/Admin
const getAllAdoptionRequests = asyncHandler(async (req, res) => {
  const adoptions = await Adoption.find()
    .populate('petId', 'name type breed imageUrl')
    .populate('userId', 'name email')
    .sort({ requestDate: -1 });
  
  res.status(200).json(adoptions);
});

// @desc    Get user's adoption requests
// @route   GET /api/pets/adoptions/user
// @access  Private
const getUserAdoptionRequests = asyncHandler(async (req, res) => {
  const adoptions = await Adoption.find({ userId: req.user.id })
    .populate('petId', 'name type breed imageUrl')
    .sort({ requestDate: -1 });
  
  res.status(200).json(adoptions);
});

// @desc    Get adoption request by ID
// @route   GET /api/pets/adoptions/:id
// @access  Private
const getAdoptionById = asyncHandler(async (req, res) => {
  const adoption = await Adoption.findById(req.params.id)
    .populate('petId', 'name type breed imageUrl')
    .populate('userId', 'name email');
  
  if (!adoption) {
    res.status(404);
    throw new Error('Adoption request not found');
  }
  
  // Check if user is authorized to view this adoption request
  if (adoption.userId._id.toString() !== req.user.id && req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to view this adoption request');
  }
  
  res.status(200).json(adoption);
});

// @desc    Update adoption request status (admin only)
// @route   PUT /api/pets/adoptions/:id
// @access  Private/Admin
const updateAdoptionStatus = asyncHandler(async (req, res) => {
  const { status, adminNotes } = req.body;
  
  if (!status) {
    res.status(400);
    throw new Error('Please provide status');
  }
  
  const adoption = await Adoption.findById(req.params.id);
  
  if (!adoption) {
    res.status(404);
    throw new Error('Adoption request not found');
  }
  
  // Only admin can update adoption status
  if (req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to update adoption status');
  }
  
  adoption.status = status;
  adoption.adminNotes = adminNotes || adoption.adminNotes;
  adoption.responseDate = new Date();
  
  const updatedAdoption = await adoption.save();
  
  // Update pet status based on adoption decision
  const pet = await Pet.findById(adoption.petId);
  if (pet) {
    if (status === 'approved') {
      pet.adoptionStatus = 'adopted';
    } else if (status === 'rejected' || status === 'cancelled') {
      pet.adoptionStatus = 'available';
    }
    await pet.save();
  }
  
  res.status(200).json(updatedAdoption);
});

// @desc    Cancel adoption request (user only)
// @route   DELETE /api/pets/adoptions/:id
// @access  Private
const cancelAdoptionRequest = asyncHandler(async (req, res) => {
  const adoption = await Adoption.findById(req.params.id);
  
  if (!adoption) {
    res.status(404);
    throw new Error('Adoption request not found');
  }
  
  // Check if user is authorized to cancel this adoption request
  if (adoption.userId.toString() !== req.user.id) {
    res.status(401);
    throw new Error('Not authorized to cancel this adoption request');
  }
  
  // Check if the adoption request is still pending
  if (adoption.status !== 'pending') {
    res.status(400);
    throw new Error('Cannot cancel a non-pending adoption request');
  }
  
  adoption.status = 'cancelled';
  adoption.responseDate = new Date();
  await adoption.save();
  
  // Update pet status to available
  const pet = await Pet.findById(adoption.petId);
  if (pet) {
    pet.adoptionStatus = 'available';
    await pet.save();
  }
  
  res.status(200).json({ id: req.params.id, message: 'Adoption request cancelled' });
});

module.exports = {
  requestAdoption,
  getAllAdoptionRequests,
  getUserAdoptionRequests,
  getAdoptionById,
  updateAdoptionStatus,
  cancelAdoptionRequest
};
