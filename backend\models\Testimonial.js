const mongoose = require('mongoose');

const testimonialSchema = mongoose.Schema(
  {
    name: {
      type: String,
      required: true
    },
    role: {
      type: String,
      default: 'Pet Parent'
    },
    content: {
      type: String,
      required: true
    },
    imageUrl: {
      type: String,
      default: 'https://via.placeholder.com/150'
    },
    rating: {
      type: Number,
      min: 1,
      max: 5,
      default: 5
    },
    category: {
      type: String,
      enum: ['adoption', 'volunteer', 'donation', 'rescue', 'general'],
      default: 'general'
    },
    featured: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: true
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true
  }
);

module.exports = mongoose.model('Testimonial', testimonialSchema);