const asyncHandler = require('express-async-handler');
const Testimonial = require('../models/Testimonial');

// @desc    Get all testimonials
// @route   GET /api/testimonials
// @access  Public
const getTestimonials = asyncHandler(async (req, res) => {
  const { category, featured } = req.query;
  
  // Build filter object
  const filter = { isActive: true };
  if (category) filter.category = category;
  if (featured === 'true') filter.featured = true;
  
  const testimonials = await Testimonial.find(filter)
    .sort({ featured: -1, order: 1, createdAt: -1 });
  
  res.status(200).json(testimonials);
});

// @desc    Create a testimonial
// @route   POST /api/testimonials
// @access  Private
const createTestimonial = asyncHandler(async (req, res) => {
  const {
    name,
    role,
    content,
    imageUrl,
    rating,
    category
  } = req.body;

  if (!content) {
    res.status(400);
    throw new Error('Please provide testimonial content');
  }

  // Use authenticated user's name if not provided
  const userName = name || req.user.name;

  const testimonial = await Testimonial.create({
    name: userName,
    role: role || 'Pet Parent',
    content,
    imageUrl: imageUrl || 'https://via.placeholder.com/150',
    rating: rating || 5,
    category: category || 'general',
    featured: false, // Only admins can set featured
    userId: req.user.id
  });

  res.status(201).json(testimonial);
});

// @desc    Update a testimonial
// @route   PUT /api/testimonials/:id
// @access  Private/Admin
const updateTestimonial = asyncHandler(async (req, res) => {
  const testimonial = await Testimonial.findById(req.params.id);
  
  if (!testimonial) {
    res.status(404);
    throw new Error('Testimonial not found');
  }
  
  // Only allow admin or the creator to update
  if (testimonial.userId && 
      testimonial.userId.toString() !== req.user.id && 
      req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to update this testimonial');
  }
  
  const updatedTestimonial = await Testimonial.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  );
  
  res.status(200).json(updatedTestimonial);
});

// @desc    Delete a testimonial
// @route   DELETE /api/testimonials/:id
// @access  Private/Admin
const deleteTestimonial = asyncHandler(async (req, res) => {
  const testimonial = await Testimonial.findById(req.params.id);
  
  if (!testimonial) {
    res.status(404);
    throw new Error('Testimonial not found');
  }
  
  // Only allow admin or the creator to delete
  if (testimonial.userId && 
      testimonial.userId.toString() !== req.user.id && 
      req.user.role !== 'admin') {
    res.status(401);
    throw new Error('Not authorized to delete this testimonial');
  }
  
  // Soft delete for admins, hard delete for users
  if (req.user.role === 'admin') {
    testimonial.isActive = false;
    await testimonial.save();
  } else {
    await testimonial.deleteOne();
  }
  
  res.status(200).json({ message: 'Testimonial removed' });
});

// @desc    Feature/unfeature a testimonial
// @route   PUT /api/testimonials/:id/feature
// @access  Private/Admin
const featureTestimonial = asyncHandler(async (req, res) => {
  const testimonial = await Testimonial.findById(req.params.id);
  
  if (!testimonial) {
    res.status(404);
    throw new Error('Testimonial not found');
  }
  
  testimonial.featured = !testimonial.featured;
  await testimonial.save();
  
  res.status(200).json({ 
    id: testimonial._id, 
    featured: testimonial.featured 
  });
});

module.exports = {
  getTestimonials,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial,
  featureTestimonial
};