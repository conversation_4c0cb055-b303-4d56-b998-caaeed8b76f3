/**
 * Auto Seed Configuration
 * 
 * This module checks if the database is empty and seeds it automatically
 * when the server starts if no data exists.
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Pet = require('../models/Pet');
const Adoption = require('../models/Adoption');

// Sample admin user
const adminUser = {
  name: 'Admin User',
  email: '<EMAIL>',
  password: 'admin123',
  role: 'admin'
};

// Sample regular user
const regularUser = {
  name: 'Regular User',
  email: '<EMAIL>',
  password: 'user123',
  location: 'New York',
  bio: 'I love animals and want to adopt a pet',
  phone: '************'
};

// Sample pets
const pets = [
  {
    name: 'Max',
    type: 'dog',
    breed: 'Labrador Retriever',
    age: 3,
    gender: 'male',
    size: 'large',
    description: '<PERSON> is a friendly and energetic Labrador who loves to play fetch and go for long walks.',
    healthStatus: 'healthy',
    vaccinated: true,
    neutered: true,
    adoptionStatus: 'available',
    imageUrl: 'https://images.unsplash.com/photo-**********-00a7907e9de1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
  },
  {
    name: 'Bella',
    type: 'cat',
    breed: 'Siamese',
    age: 2,
    gender: 'female',
    size: 'medium',
    description: 'Bella is a graceful Siamese cat who enjoys lounging in sunny spots and playing with string toys.',
    healthStatus: 'healthy',
    vaccinated: true,
    neutered: true,
    adoptionStatus: 'available',
    imageUrl: 'https://images.unsplash.com/photo-1513360371669-4adf3dd7dff8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
  },
  {
    name: 'Charlie',
    type: 'dog',
    breed: 'Beagle',
    age: 1,
    gender: 'male',
    size: 'medium',
    description: 'Charlie is a curious and playful Beagle puppy who loves to explore and make new friends.',
    healthStatus: 'healthy',
    vaccinated: true,
    neutered: false,
    adoptionStatus: 'available',
    imageUrl: 'https://images.unsplash.com/photo-1505628346881-b72b27e84530?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
  }
];

/**
 * Check if the database is empty and seed it if needed
 */
const checkAndSeedDatabase = async () => {
  try {
    // Check if there are any users in the database
    const userCount = await User.countDocuments();
    
    if (userCount === 0) {
      console.log('🌱 Database is empty. Starting automatic seeding...');
      
      // Create admin user
      const salt = await bcrypt.genSalt(10);
      const hashedAdminPassword = await bcrypt.hash(adminUser.password, salt);
      
      const createdAdmin = await User.create({
        name: adminUser.name,
        email: adminUser.email,
        password: hashedAdminPassword,
        role: adminUser.role
      });
      
      console.log(`✅ Admin user created: ${createdAdmin.name} (${createdAdmin.email})`);
      
      // Create regular user
      const hashedUserPassword = await bcrypt.hash(regularUser.password, salt);
      
      const createdUser = await User.create({
        name: regularUser.name,
        email: regularUser.email,
        password: hashedUserPassword,
        location: regularUser.location,
        bio: regularUser.bio,
        phone: regularUser.phone
      });
      
      console.log(`✅ Regular user created: ${createdUser.name} (${createdUser.email})`);
      
      // Create pets
      for (const pet of pets) {
        const createdPet = await Pet.create({
          ...pet,
          createdBy: createdAdmin._id
        });
        
        console.log(`✅ Pet created: ${createdPet.name} (${createdPet.type}, ${createdPet.breed})`);
      }
      
      console.log('✅ Database seeded successfully!');
    } else {
      console.log('📊 Database already contains data. Skipping automatic seeding.');
    }
  } catch (error) {
    console.error('❌ Error during automatic seeding:', error);
  }
};

module.exports = checkAndSeedDatabase;
